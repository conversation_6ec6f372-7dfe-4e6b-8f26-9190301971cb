<script setup>
import { ref, reactive, watch, onMounted } from 'vue';
import { Search, Plus, Edit, Delete, View, House, Loading } from '@element-plus/icons-vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import BackHeader from '@/components/BackHeader.vue';
import { getUserListApi } from '@/request/userApi';
import { addHouseApi, getHouseListApi, getStatisticalStatus, editHouseApi, deleteHouseApi, getHouseByIdApi } from '@/request/houseApi';

// 搜索关键词
const searchKeyword = ref('');

// 房产状态统计
const statusStats = reactive([
  { name: '总房产数', count: 0, icon: 'el-icon-house', color: '#409EFF' },
  { name: '已入住', count: 0, icon: 'el-icon-user', color: '#67C23A' },
  { name: '空置', count: 0, icon: 'el-icon-empty', color: '#909399' },
  { name: '维修中', count: 0, icon: 'el-icon-tools', color: '#E6A23C' }
]);

// 加载房产状态统计
const loadStatusStats = async () => {
  try {
    const response = await getStatisticalStatus();
    if (response.code === 200 && response.data) {
      console.log(response.data)
      // 处理数组格式的响应
      response.data.forEach(item => {
        if (item.name === '总房产数') statusStats[0].count = item.value;
        else if (item.name === '已入住') statusStats[1].count = item.value;
        else if (item.name === '空置') statusStats[2].count = item.value;
        else if (item.name === '维修中') statusStats[3].count = item.value;
      });
    } else {
      ElMessage.error('获取房产状态统计失败');
    }
  } catch (error) {
    console.error('获取房产状态统计失败:', error);
    ElMessage.error('获取房产状态统计失败，请稍后重试');
  }
}; // 房产数据
const houses = reactive([]);
const loadingHouses = ref(false);

// 添加房产对话框相关
const addDialogVisible = ref(false);
const addFormRef = ref(null);
const isSubmitting = ref(false);

// 查看房产详情对话框相关
const viewDialogVisible = ref(false);
const viewHouseData = reactive({});

// 编辑房产对话框相关
const editDialogVisible = ref(false);
const editFormRef = ref(null);
const isEditSubmitting = ref(false);
const editHouseData = reactive({
  houseId: '',
  room: '',
  building: '',
  type: '',
  area: '',
  status: '0',
  rent: '',
  conditions: '1',
  userId: ''
});

// 新房产表单数据
const newHouse = reactive({
  room: '',
  building: '',
  type: '',
  area: '',
  status: '0', // 0空置 1已入住 2维修中
  rent: '',
  conditions: '1', // 0优良，1良好，2维修中
  userId: ''
});

// 住户列表数据
const residentOptions = reactive([]);
const loadingResidents = ref(false);

// 表单验证规则
const rules = {
  room: [
    { required: true, message: '请输入房号', trigger: 'blur' },
    { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }
  ],
  building: [
    { required: true, message: '请选择楼栋', trigger: 'change' }
  ],
  type: [
    { required: true, message: '请选择户型', trigger: 'change' }
  ],
  area: [
    { required: true, message: '请输入面积', trigger: 'blur' },
    { pattern: /^\d+(\.\d{1,2})?$/, message: '请输入正确的面积', trigger: 'blur' }
  ],
  rent: [
    { required: true, message: '请输入租金', trigger: 'blur' },
    { pattern: /^\d+(\.\d{1,2})?$/, message: '请输入正确的金额', trigger: 'blur' }
  ],
  userId: [
    { required: true, message: '请选择住户', trigger: 'change' }
  ],
  conditions: [
    { required: true, message: '请选择房屋状况', trigger: 'change' }
  ]
};

// 数据转换函数
const getTypeLabel = (type) => {
  const typeMap = {
    0: '一室一厅',
    1: '一室两厅',
    2: '两室一厅',
    3: '两室两厅',
    4: '三室一厅',
    5: '三室两厅',
    6: '四室两厅'
  };
  return typeMap[type] || '未知户型';
};

const getStatusLabel = (status) => {
  const statusMap = {
    0: '空置',
    1: '已入住',
    2: '维修中'
  };
  return statusMap[status] || '未知状态';
};

const getStatusClass = (status) => {
  const classMap = {
    0: 'status-vacant',
    1: 'status-occupied',
    2: 'status-maintenance'
  };
  return classMap[status] || '';
};

const getConditionLabel = (condition) => {
  const conditionMap = {
    0: '优良',
    1: '良好',
    2: '维修中'
  };
  return conditionMap[condition] || '未知状况';
};

const getStatusCode = (statusLabel) => {
  const statusMap = {
    '空置': '0',
    '已入住': '1',
    '维修中': '2'
  };
  return statusMap[statusLabel];
};

// 根据userId查找住户名称
const getResidentNameById = (userId) => {
  if (!userId) return '';
  const resident = residentOptions.find(option => option.value === userId.toString());
  return resident ? resident.label : '';
};

// 获取住户列表
const loadResidentOptions = async () => {
  try {
    loadingResidents.value = true;
    const response = await getUserListApi({
      pageNum: 1,
      pageSize: 1000, // 获取所有住户
      status: '1' // 只获取启用状态的住户
    });

    if (response.code === 200 && response.data) {
      // 清空现有数据
      residentOptions.length = 0;
      // 添加新数据
      response.data.forEach(user => {
        const option = {
          value: user.userId.toString(),
          label: user.nickname || user.username
        };
        residentOptions.push(option);
      });
    } else {
      ElMessage.error('获取住户列表失败');
    }
  } catch (error) {
    console.error('获取住户列表失败:', error);
    ElMessage.error('获取住户列表失败，请稍后重试');
  } finally {
    loadingResidents.value = false;
  }
};

// 获取房产列表
const loadHouseList = async () => {
  try {
    loadingHouses.value = true;
    const response = await getHouseListApi({
      pageNum: currentPage.value,
      pageSize: 10,
      searchKey: searchKeyword.value || undefined,
      status: filterValue.value === '全部状态' ? undefined : getStatusCode(filterValue.value)
    });

    if (response.code === 200 && response.data) {
      // 清空现有数据
      houses.length = 0;
      // 添加新数据
      response.data.forEach(house => {
        console.log('房产列表 - 原始房产数据:', house); // 调试信息
        const processedHouse = {
          id: house.houseId || house.id,
          houseId: house.houseId || house.id,
          room: house.room,
          building: house.building,
          type: getTypeLabel(house.type),
          area: `${house.area}㎡`,
          status: getStatusLabel(house.status),
          statusClass: getStatusClass(house.status),
          owner: house.nickname || '--',
          rent: `¥${house.rent}`,
          conditions: getConditionLabel(house.conditions)
        };
        console.log('房产列表 - 处理后的房产数据:', processedHouse); // 调试信息
        houses.push(processedHouse);
      });

      // 更新总数
      total.value = response.total || houses.length;

    } else {
      ElMessage.error('获取房产列表失败');
    }
  } catch (error) {
    console.error('获取房产列表失败:', error);
    ElMessage.error('获取房产列表失败，请稍后重试');
  } finally {
    loadingHouses.value = false;
  }
};

// 处理搜索
const handleSearch = () => {
  currentPage.value = 1; // 重置到第一页
  loadHouseList();
};

// 处理筛选
const handleFilter = (filterItem) => {
  filterValue.value = filterItem;
  currentPage.value = 1; // 重置到第一页
  loadHouseList();
};

// 锁定页面滚动
const lockBodyScroll = () => {
  const scrollY = window.scrollY;
  document.documentElement.classList.add('dialog-open');
  document.body.classList.add('dialog-open');
  document.body.style.overflow = 'hidden';
  document.body.style.position = 'fixed';
  document.body.style.top = `-${scrollY}px`;
  document.body.style.left = '0';
  document.body.style.right = '0';
  document.body.style.width = '100%';
  document.body.style.height = '100%';
  document.body.dataset.scrollY = scrollY.toString();
};

// 解锁页面滚动
const unlockBodyScroll = () => {
  const scrollY = parseInt(document.body.dataset.scrollY || '0');
  document.documentElement.classList.remove('dialog-open');
  document.body.classList.remove('dialog-open');
  document.body.style.overflow = '';
  document.body.style.position = '';
  document.body.style.top = '';
  document.body.style.left = '';
  document.body.style.right = '';
  document.body.style.width = '';
  document.body.style.height = '';
  delete document.body.dataset.scrollY;
  window.scrollTo(0, scrollY);
};

// 添加新房产
const addNewHouse = async () => {
  addDialogVisible.value = true;
  lockBodyScroll(); // 锁定页面滚动

  // 加载住户列表
  await loadResidentOptions();

  // 重置表单
  if (addFormRef.value) {
    addFormRef.value.resetFields();
  }
  // 重置表单数据
  Object.assign(newHouse, {
    room: '',
    building: '',
    type: '',
    area: '',
    status: '0', // 0空置 1已入住 2维修中
    rent: '',
    conditions: '1', // 0优良，1良好，2维修中
    userId: ''
  });
};

// 提交新房产表单
const submitNewHouse = async () => {
  if (!addFormRef.value) return;

  // 动态验证：如果房屋状态不为空置，则住户字段必填
  if (newHouse.status !== '0' && !newHouse.userId) { // 0空置
    ElMessage.error('请选择户主');
    return;
  }

  await addFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        isSubmitting.value = true;

        // 调用添加房产API
        const response = await addHouseApi({
          userId: parseInt(newHouse.userId) || 0, // 住户ID，空置时为0
          type: newHouse.type, // 户型
          area: parseFloat(newHouse.area),
          building: newHouse.building, // 添加楼栋信息
          room: newHouse.room, // 添加房号信息
          status: newHouse.status, // 状态
          rent: parseFloat(newHouse.rent),
          conditions: newHouse.conditions, // 房屋状况
        });

        if (response.code === 200) {
          ElMessage.success('添加房产成功');
          addDialogVisible.value = false;
          unlockBodyScroll(); // 解锁页面滚动

          // 重新加载房产列表
          await loadHouseList();
        } else {
          ElMessage.error(response.msg || '添加房产失败');
        }
      } catch (error) {
        console.error('添加房产失败:', error);
        ElMessage.error('添加房产失败，请稍后重试');
      } finally {
        isSubmitting.value = false;
      }
    }
  });
};

// 取消添加
const cancelAdd = () => {
  addDialogVisible.value = false;
  unlockBodyScroll(); // 解锁页面滚动
};


// 查看房产详情
const viewHouseDetail = async (house) => {
  try {
    const houseId = house.houseId || house.id;
    const response = await getHouseByIdApi(houseId);
    if (response.code === 200 && response.data) {
      Object.assign(viewHouseData, {
        ...response.data,
        typeLabel: getTypeLabel(response.data.type),
        statusLabel: getStatusLabel(response.data.status),
        conditionLabel: getConditionLabel(response.data.conditions)
      });
      viewDialogVisible.value = true;
      lockBodyScroll();
    } else {
      ElMessage.error('获取房产详情失败');
    }
  } catch (error) {
    console.error('获取房产详情失败:', error);
    ElMessage.error('获取房产详情失败，请稍后重试');
  }
};

// 关闭查看详情对话框
const closeViewDialog = () => {
  viewDialogVisible.value = false;
  unlockBodyScroll();
};

// 编辑房产
const editHouse = async (house) => {
  try {
    const houseId = house.houseId || house.id;

    // 先加载住户列表
    await loadResidentOptions();

    const response = await getHouseByIdApi(houseId);
    if (response.code === 200 && response.data) {
      console.log('编辑房产 - API返回的数据:', response.data); // 调试信息

      Object.assign(editHouseData, {
        houseId: response.data.houseId || response.data.id,
        room: response.data.room,
        building: response.data.building,
        type: response.data.type,
        area: response.data.area,
        status: response.data.status,
        rent: response.data.rent,
        conditions: response.data.conditions,
        userId: response.data.userId ? response.data.userId.toString() : ''
      });

      editDialogVisible.value = true;
      lockBodyScroll();

      // 重置表单验证状态
      if (editFormRef.value) {
        editFormRef.value.clearValidate();
      }
    } else {
      ElMessage.error('获取房产信息失败');
    }
  } catch (error) {
    console.error('获取房产信息失败:', error);
    ElMessage.error('获取房产信息失败，请稍后重试');
  }
};

// 提交编辑房产表单
const submitEditHouse = async () => {
  if (!editFormRef.value) return;

  // 动态验证：如果房屋状态不为空置，则住户字段必填
  if (editHouseData.status !== '0' && !editHouseData.userId) {
    ElMessage.error('请选择户主');
    return;
  }

  await editFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        isEditSubmitting.value = true;

        // 确保houseId存在
        if (!editHouseData.houseId) {
          ElMessage.error('房产ID缺失，无法编辑');
          return;
        }

        console.log('编辑房产数据:', editHouseData); // 调试信息

        const response = await editHouseApi({
          houseId: parseInt(editHouseData.houseId),
          userId: parseInt(editHouseData.userId) || 0,
          building: editHouseData.building,
          room: editHouseData.room,
          rent: parseFloat(editHouseData.rent),
          conditions: editHouseData.conditions,
          area: parseFloat(editHouseData.area),
          type: editHouseData.type,
          status: editHouseData.status
        });

        if (response.code === 200) {
          ElMessage.success('编辑房产成功');
          editDialogVisible.value = false;
          unlockBodyScroll();

          // 重新加载房产列表
          await loadHouseList();
        } else {
          ElMessage.error(response.msg || '编辑房产失败');
        }
      } catch (error) {
        console.error('编辑房产失败:', error);
        ElMessage.error('编辑房产失败，请稍后重试');
      } finally {
        isEditSubmitting.value = false;
      }
    }
  });
};

// 取消编辑
const cancelEdit = () => {
  editDialogVisible.value = false;
  unlockBodyScroll();
};

// 删除房产
const deleteHouse = async (house) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除房产 "${house.building} ${house.room}" 吗？此操作不可撤销。`,
      '删除确认',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning',
        confirmButtonClass: 'el-button--danger'
      }
    );

    const houseId = house.houseId || house.id;
    const response = await deleteHouseApi(houseId);
    if (response.code === 200) {
      ElMessage.success('删除房产成功');
      // 重新加载房产列表
      await loadHouseList();
    } else {
      ElMessage.error(response.msg || '删除房产失败');
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除房产失败:', error);
      ElMessage.error('删除房产失败，请稍后重试');
    }
  }
};

// 筛选条件
const filterValue = ref('全部状态');

// 当前页码
const currentPage = ref(1);
// 总条数
const total = ref(0);

// 处理页码变化
const handleCurrentChange = (val) => {
  currentPage.value = val;
  loadHouseList();
};

// 阻止滚动事件
const preventScroll = (e) => {
  e.preventDefault();
  e.stopPropagation();
  return false;
};

// 监听房屋状态变化
watch(() => newHouse.status, (newStatus) => {
  // 当状态改为空置时，清空住户选择
  if (newStatus === '0') { // 0空置
    newHouse.userId = '';
  }
});

// 监听对话框状态变化
watch(addDialogVisible, (newVal) => {
  if (newVal) {
    // 对话框打开时添加滚动阻止事件
    document.addEventListener('wheel', preventScroll, { passive: false });
    document.addEventListener('touchmove', preventScroll, { passive: false });
    document.addEventListener('scroll', preventScroll, { passive: false });
    window.addEventListener('scroll', preventScroll, { passive: false });
  } else {
    // 对话框关闭时移除滚动阻止事件并解锁滚动
    document.removeEventListener('wheel', preventScroll);
    document.removeEventListener('touchmove', preventScroll);
    document.removeEventListener('scroll', preventScroll);
    window.removeEventListener('scroll', preventScroll);
    unlockBodyScroll();
  }
});

// 组件挂载时加载数据
onMounted(() => {
  loadResidentOptions();
  loadHouseList();
  loadStatusStats();
});
</script>

<template>
  <BackHeader activeMenu="houses">
    <div class="houses-container">
      <!-- 页面标题和添加按钮 -->
      <div class="page-header">
        <div class="title-section">
          <h1 class="page-title">房产管理</h1>
          <p class="page-subtitle">管理小区房产信息和租赁状况</p>
        </div>

        <el-button type="primary" class="add-house-btn" @click="addNewHouse">
          <el-icon><Plus /></el-icon>
          添加房产
        </el-button>
      </div>

      <!-- 添加房产对话框 -->
      <el-dialog
        v-model="addDialogVisible"
        title="添加新房产"
        width="800px"
        :close-on-click-modal="false"
        :modal="true"
        :lock-scroll="true"
        :center="true"
        :destroy-on-close="true"
        class="add-house-dialog"
      >
        <div class="dialog-content">
          <el-form
            ref="addFormRef"
            :model="newHouse"
            :rules="rules"
            label-width="100px"
            label-position="right"
            class="house-form"
          >
            <!-- 房产信息 -->
            <div class="form-section">
              <h3 class="section-title">
                <el-icon><House /></el-icon>
                房产信息
              </h3>

              <el-form-item label="房号" prop="room">
                <el-input
                  v-model="newHouse.room"
                  placeholder="请输入房号，如：1201"
                />
              </el-form-item>

              <el-form-item label="楼栋" prop="building">
                <el-select
                  v-model="newHouse.building"
                  placeholder="请选择楼栋"
                  style="width: 100%"
                >
                  <el-option label="A栋" value="A栋" />
                  <el-option label="B栋" value="B栋" />
                  <el-option label="C栋" value="C栋" />
                  <el-option label="D栋" value="D栋" />
                  <el-option label="E栋" value="E栋" />
                  <el-option label="F栋" value="F栋" />
                </el-select>
              </el-form-item>

              <el-form-item label="户型" prop="type">
                <el-select
                  v-model="newHouse.type"
                  placeholder="请选择户型"
                  style="width: 100%"
                >
                  <el-option label="一室一厅" value="0" />
                  <el-option label="一室两厅" value="1" />
                  <el-option label="两室一厅" value="2" />
                  <el-option label="两室两厅" value="3" />
                  <el-option label="三室一厅" value="4" />
                  <el-option label="三室两厅" value="5" />
                  <el-option label="四室两厅" value="6" />
                </el-select>
              </el-form-item>

              <el-form-item label="面积" prop="area">
                <el-input
                  v-model="newHouse.area"
                  placeholder="请输入面积"
                >
                  <template #append>㎡</template>
                </el-input>
              </el-form-item>

              <el-form-item label="房屋状态" prop="status">
                <el-select
                  v-model="newHouse.status"
                  placeholder="请选择房屋状态"
                  style="width: 100%"
                >
                  <el-option label="空置" value="0" />
                  <el-option label="已入住" value="1" />
                  <el-option label="维修中" value="2" />
                </el-select>
              </el-form-item>

              <el-form-item
                v-if="newHouse.status !== '0'"
                label="户主"
                prop="userId"
              >
                <el-select
                  v-model="newHouse.userId"
                  placeholder="请选择户主"
                  style="width: 100%"
                  clearable
                  :loading="loadingResidents"
                  loading-text="加载住户列表中..."
                  no-data-text="暂无住户数据"
                >
                  <el-option
                    v-for="userId in residentOptions"
                    :key="userId.value"
                    :label="userId.label"
                    :value="userId.value"
                  />
                </el-select>
              </el-form-item>

              <el-form-item label="租金" prop="rent">
                <el-input
                  v-model="newHouse.rent"
                  placeholder="请输入租金"
                >
                  <template #prepend>¥</template>
                </el-input>
              </el-form-item>

              <el-form-item label="房屋状况" prop="conditions">
                <el-select
                  v-model="newHouse.conditions"
                  placeholder="请选择房屋状况"
                  style="width: 100%"
                >
                  <el-option label="优良" value="0" />
                  <el-option label="良好" value="1" />
                  <el-option label="维修中" value="2" />
                </el-select>
              </el-form-item>
            </div>

          </el-form>
        </div>

        <template #footer>
          <div class="dialog-footer">
            <el-button @click="cancelAdd">取消</el-button>
            <el-button
              type="primary"
              @click="submitNewHouse"
              :loading="isSubmitting"
            >
              {{ isSubmitting ? '添加中...' : '确认添加' }}
            </el-button>
          </div>
        </template>
      </el-dialog>

      <!-- 查看房产详情对话框 -->
      <el-dialog
        v-model="viewDialogVisible"
        title="房产详情"
        width="600px"
        :close-on-click-modal="false"
        :modal="true"
        :lock-scroll="true"
        :center="true"
        :destroy-on-close="true"
        class="view-house-dialog"
      >
        <div class="view-content">
          <div class="detail-section">
            <h3 class="section-title">
              <el-icon><House /></el-icon>
              基本信息
            </h3>
            <div class="detail-grid">
              <div class="detail-item">
                <span class="detail-label">房号：</span>
                <span class="detail-value">{{ viewHouseData.room }}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">楼栋：</span>
                <span class="detail-value">{{ viewHouseData.building }}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">户型：</span>
                <span class="detail-value">{{ viewHouseData.typeLabel }}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">面积：</span>
                <span class="detail-value">{{ viewHouseData.area }}㎡</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">状态：</span>
                <el-tag
                  :type="viewHouseData.status === '1' ? 'success' :
                      viewHouseData.status === '0' ? 'info' : 'warning'"
                  size="small"
                >
                  {{ viewHouseData.statusLabel }}
                </el-tag>
              </div>
              <div class="detail-item">
                <span class="detail-label">房屋状况：</span>
                <el-tag
                  :type="viewHouseData.conditions === '0' ? 'success' :
                      viewHouseData.conditions === '1' ? 'info' : 'warning'"
                  size="small"
                  effect="plain"
                >
                  {{ viewHouseData.conditionLabel }}
                </el-tag>
              </div>
              <div class="detail-item">
                <span class="detail-label">租金：</span>
                <span class="detail-value rent-highlight">¥{{ viewHouseData.rent }}</span>
              </div>
              <div v-if="viewHouseData.nickname" class="detail-item">
                <span class="detail-label">户主：</span>
                <span class="detail-value">{{ viewHouseData.nickname }}</span>
              </div>
            </div>
          </div>
        </div>

        <template #footer>
          <div class="dialog-footer">
            <el-button @click="closeViewDialog">关闭</el-button>
          </div>
        </template>
      </el-dialog>

      <!-- 编辑房产对话框 -->
      <el-dialog
        v-model="editDialogVisible"
        title="编辑房产"
        width="800px"
        :close-on-click-modal="false"
        :modal="true"
        :lock-scroll="true"
        :center="true"
        :destroy-on-close="true"
        class="edit-house-dialog"
      >
        <div class="dialog-content">
          <el-form
            ref="editFormRef"
            :model="editHouseData"
            :rules="rules"
            label-width="100px"
            label-position="right"
            class="house-form"
          >
            <!-- 房产信息 -->
            <div class="form-section">
              <h3 class="section-title">
                <el-icon><House /></el-icon>
                房产信息
              </h3>

              <el-form-item label="房号" prop="room">
                <el-input
                  v-model="editHouseData.room"
                  placeholder="请输入房号，如：1201"
                />
              </el-form-item>

              <el-form-item label="楼栋" prop="building">
                <el-select
                  v-model="editHouseData.building"
                  placeholder="请选择楼栋"
                  style="width: 100%"
                >
                  <el-option label="A栋" value="A栋" />
                  <el-option label="B栋" value="B栋" />
                  <el-option label="C栋" value="C栋" />
                  <el-option label="D栋" value="D栋" />
                  <el-option label="E栋" value="E栋" />
                  <el-option label="F栋" value="F栋" />
                </el-select>
              </el-form-item>

              <el-form-item label="户型" prop="type">
                <el-select
                  v-model="editHouseData.type"
                  placeholder="请选择户型"
                  style="width: 100%"
                >
                  <el-option label="一室一厅" value="0" />
                  <el-option label="一室两厅" value="1" />
                  <el-option label="两室一厅" value="2" />
                  <el-option label="两室两厅" value="3" />
                  <el-option label="三室一厅" value="4" />
                  <el-option label="三室两厅" value="5" />
                  <el-option label="四室两厅" value="6" />
                </el-select>
              </el-form-item>

              <el-form-item label="面积" prop="area">
                <el-input
                  v-model="editHouseData.area"
                  placeholder="请输入面积"
                >
                  <template #append>㎡</template>
                </el-input>
              </el-form-item>

              <el-form-item label="房屋状态" prop="status">
                <el-select
                  v-model="editHouseData.status"
                  placeholder="请选择房屋状态"
                  style="width: 100%"
                >
                  <el-option label="空置" value="0" />
                  <el-option label="已入住" value="1" />
                  <el-option label="维修中" value="2" />
                </el-select>
              </el-form-item>

              <el-form-item
                v-if="editHouseData.status !== '0'"
                label="户主"
                prop="userId"
              >
                <el-select
                  v-model="editHouseData.userId"
                  placeholder="请选择户主"
                  style="width: 100%"
                  clearable
                  :loading="loadingResidents"
                  loading-text="加载住户列表中..."
                  no-data-text="暂无住户数据"
                >
                  <el-option
                    v-for="userId in residentOptions"
                    :key="userId.value"
                    :label="userId.label"
                    :value="userId.value"
                  />
                </el-select>
              </el-form-item>

              <el-form-item label="租金" prop="rent">
                <el-input
                  v-model="editHouseData.rent"
                  placeholder="请输入租金"
                >
                  <template #prepend>¥</template>
                </el-input>
              </el-form-item>

              <el-form-item label="房屋状况" prop="conditions">
                <el-select
                  v-model="editHouseData.conditions"
                  placeholder="请选择房屋状况"
                  style="width: 100%"
                >
                  <el-option label="优良" value="0" />
                  <el-option label="良好" value="1" />
                  <el-option label="维修中" value="2" />
                </el-select>
              </el-form-item>
            </div>

          </el-form>
        </div>

        <template #footer>
          <div class="dialog-footer">
            <el-button @click="cancelEdit">取消</el-button>
            <el-button
              type="primary"
              @click="submitEditHouse"
              :loading="isEditSubmitting"
            >
              {{ isEditSubmitting ? '保存中...' : '保存修改' }}
            </el-button>
          </div>
        </template>
      </el-dialog>

      <!-- 状态统计卡片 -->
      <div class="status-cards">
        <div v-for="(stat, index) in statusStats" :key="index" class="status-card" :class="`status-card-${index}`">
          <div class="status-icon" :style="{ backgroundColor: stat.color + '20' }">
            <el-icon v-if="index === 0"><el-icon-office-building /></el-icon>
            <el-icon v-else-if="index === 1"><el-icon-user /></el-icon>
            <el-icon v-else-if="index === 2"><el-icon-house /></el-icon>
            <el-icon v-else><el-icon-tools /></el-icon>
          </div>
          <div class="status-info">
            <div class="status-count">{{ stat.count }}</div>
            <div class="status-name">{{ stat.name }}</div>
          </div>
        </div>
      </div>

      <!-- 搜索和筛选 -->
      <div class="search-filter-bar">
        <div class="search-box">
          <el-input
              v-model="searchKeyword"
              placeholder="搜索房号、住户"
              class="search-input"
              @keyup.enter="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </div>

        <div class="filter-box">
          <el-dropdown>
            <el-button class="filter-button">
              {{ filterValue }}
              <el-icon class="el-icon--right"><arrow-down /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item @click="handleFilter('全部状态')">全部状态</el-dropdown-item>
                <el-dropdown-item @click="handleFilter('已入住')">已入住</el-dropdown-item>
                <el-dropdown-item @click="handleFilter('空置')">空置</el-dropdown-item>
                <el-dropdown-item @click="handleFilter('维修中')">维修中</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>

      <!-- 房产列表容器 -->
      <div class="houses-list-container">
        <!-- 表头 -->
        <div class="houses-list-header">
          <div class="header-item header-id">房号</div>
          <div class="header-item header-type">户型</div>
          <div class="header-item header-status">状态</div>
          <div class="header-item header-owner">户主</div>
          <div class="header-item header-rent">租金</div>
          <div class="header-item header-condition">房屋状况</div>
          <div class="header-item header-actions">操作</div>
        </div>

        <!-- 加载状态 -->
        <div v-if="loadingHouses" class="loading-container">
          <el-icon class="is-loading"><Loading /></el-icon>
          <span>加载中...</span>
        </div>

        <!-- 空数据状态 -->
        <div v-else-if="houses.length === 0" class="empty-container">
          <el-empty description="暂无房产数据" />
        </div>

        <!-- 房产列表 -->
        <div v-else v-for="house in houses" :key="house.id" class="house-item">
          <div class="house-id">
            <el-icon><House /></el-icon>
            {{ house.room }}
          </div>

          <div class="house-type">
            <div>{{ house.type }}</div>
            <div class="house-area">{{ house.area }}</div>
          </div>

          <div class="house-status">
            <el-tag
                :type="house.status === '已入住' ? 'success' :
                    house.status === '空置' ? 'info' : 'warning'"
                size="small"
            >
              {{ house.status }}
            </el-tag>
          </div>

          <div class="house-owner">
            <span v-if="house.owner !== '-'">{{ house.owner }}</span>
            <span v-else class="no-data">-</span>
          </div>

          <div class="house-rent">
            <span class="rent-value">{{ house.rent }}</span>
          </div>

          <div class="house-condition">
            <el-tag
                :type="house.conditions === '优良' ? 'success' :
                    house.conditions === '良好' ? 'info' : 'warning'"
                size="small"
                effect="plain"
            >
              {{ house.conditions }}
            </el-tag>
          </div>

           <div class="house-actions">
            <el-button type="primary" size="large" text @click="viewHouseDetail(house)" title="查看详情">
              <el-icon :size="20"><View /></el-icon>
            </el-button>
            <el-button type="primary" size="large" text @click="editHouse(house)" title="编辑房产">
              <el-icon :size="20"><Edit /></el-icon>
            </el-button>
            <el-button type="danger" size="large" text @click="deleteHouse(house)" title="删除房产">
              <el-icon :size="20"><Delete /></el-icon>
            </el-button>
          </div>
        </div>
      </div>

      <!-- 分页 -->
      <div class="pagination-container">
        <div class="total-info">共 {{ total }} 条记录</div>
        <el-pagination
            background
            layout="prev, pager, next"
            :total="total"
            :current-page="currentPage"
            @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </BackHeader>
</template>

<style scoped>
.houses-container {
  padding: 0 20px 20px;
  max-width: 1800px;
  margin: 0 auto;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.page-title {
  font-size: 28px;
  font-weight: 600;
  margin: 0;
  color: #333;
}

.page-subtitle {
  font-size: 16px;
  color: #909399;
  margin: 10px 0 0 0;
}

.add-house-btn {
  display: flex;
  align-items: center;
  gap: 10px;
  height: 48px;
  font-size: 16px;
  padding: 0 20px;
}

.status-cards {
  display: flex;
  gap: 24px;
  margin-bottom: 32px;
}

.status-card {
  flex: 1;
  background-color: #fff;
  border-radius: 12px;
  padding: 28px;
  display: flex;
  align-items: center;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
}

.status-icon {
  width: 64px;
  height: 64px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20px;
}

.status-icon .el-icon {
  font-size: 32px;
  color: #409EFF;
}

.status-card-0 .status-icon .el-icon {
  color: #409EFF;
}

.status-card-1 .status-icon .el-icon {
  color: #67C23A;
}

.status-card-2 .status-icon .el-icon {
  color: #909399;
}

.status-card-3 .status-icon .el-icon {
  color: #E6A23C;
}

.status-info {
  display: flex;
  flex-direction: column;
}

.status-count {
  font-size: 32px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
}

.status-name {
  font-size: 16px;
  color: #909399;
}

.search-filter-bar {
  display: flex;
  margin-bottom: 32px;
}

.search-box {
  flex: 1;
  margin-right: 20px;
}

.search-input {
  width: 100%;
}

.search-input :deep(.el-input__inner) {
  height: 52px;
  font-size: 16px;
  padding: 0 20px;
}

.filter-button {
  height: 52px;
  font-size: 16px;
  padding: 0 20px;
}

/* 房产列表容器样式 */
.houses-list-container {
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  margin-bottom: 32px;
  overflow: hidden;
  padding: 24px;
}

/* 表头样式 */
.houses-list-header {
  display: flex;
  padding: 16px 0;
  background-color: #f5f7fa;
  color: #606266;
  font-weight: 500;
  font-size: 16px; /* 增大字体 */
  border-radius: 8px; /* 添加圆角 */
  margin-bottom: 10px; /* 增加与列表项的间距 */
}

.header-item {
  flex: 1; /* 所有列平均分配空间 */
  padding: 0 15px; /* 统一内边距 */
  text-align: left;
}

.header-id {
  flex: 0.8; /* 房号列稍窄 */
}

.header-type {
  flex: 1; /* 户型列标准宽度 */
}

.header-status {
  flex: 0.8; /* 状态列稍窄 */
}

.header-owner {
  flex: 0.8; /* 住户列稍窄 */
}

.header-rent {
  flex: 0.8; /* 租金列稍窄 */
}

.header-condition {
  flex: 1; /* 房屋状况列标准宽度 */
}

.header-actions {
  flex: 0.8; /* 操作列稍窄 */
  text-align: center;
}

/* 房产项目样式 */
.house-item {
  display: flex;
  padding: 20px 0; /* 增加上下内边距 */
  border-bottom: 1px solid #f0f0f0;
  align-items: center;
}

.house-item:last-child {
  border-bottom: none;
}

.house-id {
  flex: 0.8; /* 与表头对应 */
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
  padding: 0 15px; /* 统一内边距 */
}

.house-type {
  flex: 1; /* 与表头对应 */
  display: flex;
  flex-direction: column;
  padding: 0 15px; /* 统一内边距 */
}

.house-area {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.house-status {
  flex: 0.8; /* 与表头对应 */
  padding: 0 15px; /* 统一内边距 */
}

.house-owner {
  flex: 0.8; /* 与表头对应 */
  padding: 0 15px; /* 统一内边距 */
}

.house-rent {
  flex: 0.8; /* 与表头对应 */
  padding: 0 15px; /* 统一内边距 */
}

.rent-value {
  font-weight: 500;
  color: #606266;
}

.house-condition {
  flex: 1; /* 与表头对应 */
  padding: 0 15px; /* 统一内边距 */
}

.house-actions {
  flex: 0.8; /* 与表头对应 */
  display: flex;
  justify-content: center;
  gap: 12px; /* 增加按钮间距 */
}

.house-actions .el-button {
  padding: 8px; /* 增加按钮内边距 */
}

.no-data {
  color: #c0c4cc;
}

/* 加载状态样式 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  color: #909399;
  font-size: 16px;
}

.loading-container .el-icon {
  font-size: 32px;
  margin-bottom: 16px;
}

/* 空数据状态样式 */
.empty-container {
  padding: 40px 20px;
}

.pagination-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 24px;
}

.total-info {
  font-size: 15px;
  color: #909399;
}

/* 响应式调整 */
@media (max-width: 1200px) {
  .header-condition, .house-condition {
    display: none;
  }

  .header-item, .house-id, .house-type, .house-status,
  .house-owner, .house-rent, .house-actions {
    padding: 0 10px; /* 减少内边距 */
  }
}

@media (max-width: 768px) {
  .header-rent, .house-rent,
  .header-owner, .house-owner {
    display: none;
  }

  .house-id, .header-id {
    flex: 1;
  }

  .house-type, .header-type {
    flex: 1.5;
  }

  .house-status, .header-status {
    flex: 1;
  }

  .house-actions, .header-actions {
    flex: 1;
  }
}

/* 添加房产对话框样式 - 完全固定 */
.add-house-dialog {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  z-index: 2000 !important;
  overflow: hidden !important;
  margin: 0 !important;
  padding: 0 !important;
}

.add-house-dialog :deep(.el-overlay) {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  z-index: 2000 !important;
  background-color: rgba(0, 0, 0, 0.5) !important;
  overflow: hidden !important;
  margin: 0 !important;
  padding: 0 !important;
}

.add-house-dialog :deep(.el-overlay-dialog) {
  position: fixed !important;
  top: 30% !important;
  left: 50% !important;
  transform: translate(-50%, -30%) !important;
  margin: 0 !important;
  padding: 0 !important;
  max-height: none !important;
  overflow: visible !important;
  width: auto !important;
  height: auto !important;
}

.add-house-dialog :deep(.el-dialog) {
  position: relative !important;
  margin: 0 !important;
  padding: 0 !important;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  max-height: 70vh !important;
  width: 800px !important;
  max-width: 90vw !important;
  display: flex !important;
  flex-direction: column !important;
  overflow: hidden !important;
}

.add-house-dialog :deep(.el-dialog__header) {
  padding: 24px 24px 16px;
  border-bottom: 1px solid #f0f0f0;
  flex-shrink: 0;
  position: relative;
  z-index: 1;
}

.add-house-dialog :deep(.el-dialog__title) {
  font-size: 24px;
  font-weight: 600;
  color: #333;
}

.add-house-dialog :deep(.el-dialog__body) {
  padding: 20px;
  max-height: calc(70vh - 120px);
  overflow-y: auto;
  flex: 1;
  scrollbar-width: thin;
  scrollbar-color: #c1c1c1 #f1f1f1;
}

.add-house-dialog :deep(.el-dialog__body)::-webkit-scrollbar {
  width: 6px;
}

.add-house-dialog :deep(.el-dialog__body)::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.add-house-dialog :deep(.el-dialog__body)::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.add-house-dialog :deep(.el-dialog__body)::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

.dialog-content {
  width: 100%;
}

.house-form {
  width: 100%;
}

.form-section {
  margin-bottom: 24px;
  padding: 20px;
  background-color: #fafafa;
  border-radius: 6px;
  border: 1px solid #f0f0f0;
}

.form-section:last-child {
  margin-bottom: 0;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0 0 20px 0;
  padding-bottom: 10px;
  border-bottom: 2px solid #e6f7ff;
}

.section-title .el-icon {
  font-size: 18px;
  color: #1890ff;
}

/* 表单项样式 */
.house-form :deep(.el-form-item) {
  margin-bottom: 24px;
}

.house-form :deep(.el-form-item__label) {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  line-height: 1.4;
}

.house-form :deep(.el-input__wrapper) {
  border-radius: 6px;
  box-shadow: 0 0 0 1px #d9d9d9;
  transition: all 0.2s;
}

.house-form :deep(.el-input__wrapper:hover) {
  box-shadow: 0 0 0 1px #40a9ff;
}

.house-form :deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.house-form :deep(.el-input__inner) {
  font-size: 16px;
  color: #333;
}

.house-form :deep(.el-input-group__prepend),
.house-form :deep(.el-input-group__append) {
  background-color: #fafafa;
  border-color: #d9d9d9;
  color: #666;
  font-weight: 500;
  font-size: 16px;
}

.house-form :deep(.el-select .el-input__wrapper) {
  border-radius: 6px;
}

.house-form :deep(.el-textarea__inner) {
  border-radius: 6px;
  border-color: #d9d9d9;
  font-size: 16px;
  color: #333;
  resize: vertical;
}

.house-form :deep(.el-textarea__inner:hover) {
  border-color: #40a9ff;
}

.house-form :deep(.el-textarea__inner:focus) {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.house-form :deep(.el-checkbox-group) {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.house-form :deep(.el-checkbox) {
  margin-right: 0;
}

.house-form :deep(.el-checkbox__label) {
  font-size: 16px;
  color: #333;
}

/* 对话框底部 */
.add-house-dialog :deep(.el-dialog__footer) {
  flex-shrink: 0;
  position: relative;
  z-index: 1;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 16px 24px 24px;
  border-top: 1px solid #f0f0f0;
  margin: 0 -24px -24px;
  background-color: #fff;
}

.dialog-footer .el-button {
  min-width: 100px;
  height: 40px;
  font-size: 16px;
  border-radius: 6px;
}

/* 确保对话框始终固定 */
.add-house-dialog :deep(.el-dialog__wrapper) {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 100% !important;
  height: 100% !important;
  overflow: hidden !important;
}

/* 强制防止页面滚动 */
body.el-popup-parent--hidden,
body:has(.add-house-dialog) {
  overflow: hidden !important;
  position: fixed !important;
  width: 100% !important;
  height: 100% !important;
}

/* 全局阻止滚动 */
html:has(.add-house-dialog),
html.dialog-open {
  overflow: hidden !important;
  height: 100% !important;
}

body.dialog-open {
  overflow: hidden !important;
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  width: 100% !important;
  height: 100% !important;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .add-house-dialog :deep(.el-overlay-dialog) {
    top: 25% !important;
    transform: translate(-50%, -25%) !important;
    padding: 10px !important;
  }

  .add-house-dialog :deep(.el-dialog) {
    width: 95% !important;
    max-width: 95% !important;
    max-height: 65vh !important;
    margin: 0 !important;
  }

  .form-section {
    padding: 12px;
    margin-bottom: 16px;
  }

  .section-title {
    font-size: 16px;
    margin-bottom: 14px;
  }

  .house-form :deep(.el-col) {
    width: 100% !important;
    margin-bottom: 0;
  }

  .house-form :deep(.el-form-item) {
    margin-bottom: 12px;
  }

  .add-house-dialog :deep(.el-dialog__body) {
    max-height: calc(65vh - 100px);
    padding: 16px;
  }

  .house-form :deep(.el-checkbox-group) {
    gap: 12px;
  }
}

@media (max-height: 700px) {
  .add-house-dialog :deep(.el-overlay-dialog) {
    top: 20% !important;
    transform: translate(-50%, -20%) !important;
  }

  .add-house-dialog :deep(.el-dialog) {
    max-height: 60vh !important;
  }

  .add-house-dialog :deep(.el-dialog__body) {
    max-height: calc(60vh - 100px);
    padding: 12px;
  }

  .form-section {
    padding: 10px;
    margin-bottom: 12px;
  }
}

/* 表单验证错误样式 */
.house-form :deep(.el-form-item.is-error .el-input__wrapper) {
  box-shadow: 0 0 0 1px #ff4d4f;
}

.house-form :deep(.el-form-item.is-error .el-textarea__inner) {
  border-color: #ff4d4f;
}

.house-form :deep(.el-form-item__error) {
  font-size: 14px;
  color: #ff4d4f;
  margin-top: 4px;
}

/* 全局样式确保对话框固定 */
.add-house-dialog.el-dialog__wrapper {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  z-index: 2000 !important;
  overflow: hidden !important;
}

/* 确保遮罩层覆盖整个屏幕 */
.add-house-dialog .el-overlay {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  z-index: 2000 !important;
}

/* 查看房产详情对话框样式 */
.view-house-dialog :deep(.el-dialog) {
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
}

.view-house-dialog :deep(.el-dialog__header) {
  padding: 24px 24px 16px;
  border-bottom: 1px solid #f0f0f0;
}

.view-house-dialog :deep(.el-dialog__title) {
  font-size: 20px;
  font-weight: 600;
  color: #333;
}

.view-house-dialog :deep(.el-dialog__body) {
  padding: 20px 24px;
}

.view-content {
  width: 100%;
}

.detail-section {
  margin-bottom: 20px;
}

.detail-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px 24px;
  margin-top: 16px;
}

.detail-item {
  display: flex;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f5f5f5;
}

.detail-label {
  font-weight: 500;
  color: #666;
  min-width: 80px;
  margin-right: 12px;
}

.detail-value {
  color: #333;
  font-weight: 400;
}

.rent-highlight {
  color: #409EFF;
  font-weight: 600;
  font-size: 16px;
}

/* 编辑房产对话框样式 - 继承添加对话框样式 */
.edit-house-dialog {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  z-index: 2000 !important;
  overflow: hidden !important;
  margin: 0 !important;
  padding: 0 !important;
}

.edit-house-dialog :deep(.el-overlay) {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  z-index: 2000 !important;
  background-color: rgba(0, 0, 0, 0.5) !important;
  overflow: hidden !important;
  margin: 0 !important;
  padding: 0 !important;
}

.edit-house-dialog :deep(.el-overlay-dialog) {
  position: fixed !important;
  top: 30% !important;
  left: 50% !important;
  transform: translate(-50%, -30%) !important;
  margin: 0 !important;
  padding: 0 !important;
  max-height: none !important;
  overflow: visible !important;
  width: auto !important;
  height: auto !important;
}

.edit-house-dialog :deep(.el-dialog) {
  position: relative !important;
  margin: 0 !important;
  padding: 0 !important;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  max-height: 70vh !important;
  width: 800px !important;
  max-width: 90vw !important;
  display: flex !important;
  flex-direction: column !important;
  overflow: hidden !important;
}

.edit-house-dialog :deep(.el-dialog__header) {
  padding: 24px 24px 16px;
  border-bottom: 1px solid #f0f0f0;
  flex-shrink: 0;
  position: relative;
  z-index: 1;
}

.edit-house-dialog :deep(.el-dialog__title) {
  font-size: 24px;
  font-weight: 600;
  color: #333;
}

.edit-house-dialog :deep(.el-dialog__body) {
  padding: 20px;
  max-height: calc(70vh - 120px);
  overflow-y: auto;
  flex: 1;
  scrollbar-width: thin;
  scrollbar-color: #c1c1c1 #f1f1f1;
}

.edit-house-dialog :deep(.el-dialog__body)::-webkit-scrollbar {
  width: 6px;
}

.edit-house-dialog :deep(.el-dialog__body)::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.edit-house-dialog :deep(.el-dialog__body)::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.edit-house-dialog :deep(.el-dialog__body)::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 响应式调整 - 查看对话框 */
@media (max-width: 768px) {
  .view-house-dialog :deep(.el-dialog) {
    width: 95% !important;
    max-width: 95% !important;
    margin: 20px !important;
  }

  .detail-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .detail-item {
    padding: 8px 0;
  }

  .detail-label {
    min-width: 70px;
    font-size: 14px;
  }

  .detail-value {
    font-size: 14px;
  }
}

/* 响应式调整 - 编辑对话框 */
@media (max-width: 768px) {
  .edit-house-dialog :deep(.el-overlay-dialog) {
    top: 25% !important;
    transform: translate(-50%, -25%) !important;
    padding: 10px !important;
  }

  .edit-house-dialog :deep(.el-dialog) {
    width: 95% !important;
    max-width: 95% !important;
    max-height: 65vh !important;
    margin: 0 !important;
  }

  .edit-house-dialog :deep(.el-dialog__body) {
    max-height: calc(65vh - 100px);
    padding: 16px;
  }
}
</style>