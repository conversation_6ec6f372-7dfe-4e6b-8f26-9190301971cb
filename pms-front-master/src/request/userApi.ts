import request from "./request";
import { ReportData } from "@/types/api";

type UserEditRequest = {
    userId: number;
    nickname?: string;
    email?: string;
    phone?: string;
    avatar?: string;
}
type UserEditPwdDTO = {
    userId: number;
    oldPwd: string;
    newPwd: string;
}
type UserReport = {
    userId: number;
    username: string;
    nickname: string;
    avatar: string;
    email: string;
    phone: string;
    sex: string;
    status: string;
    roleType: string;
    loginIp: string;
    loginDate: string;
}
export const userInfoApi = (): Promise<ReportData<UserReport>> => {
    return request.get('/user/getInfo')
}
export const editCommon = (data: UserEditRequest): Promise<ReportData<any>> => {
    return request.put('/user/editCommon', data)
}
export const editAdmin = (data: UserEditRequest): Promise<ReportData<any>> => {
    return request.put('/user/editAdmin', data)
}
export const editAdminPwd = (data: UserEditPwdDTO): Promise<ReportData<any>> => {
    return request.put('/user/editAdminPwd', data)
}
export const editCommonPwd = (data: UserEditPwdDTO): Promise<ReportData<any>> => {
    return request.put('/user/editCommonPwd', data)
}
export const getUserListApi = (data: {
    pageNum: number;
    pageSize: number;
    username?: string;
    nickname?: string;
    phone?: string;
    email?: string;
    status?: string;
    roleType?: string;
}): Promise<ReportData<any>> => {
    return request.post('/user/list', data)
}

export const getUserByIdApi = (userId: number): Promise<ReportData<any>> => {
    return request.get(`/user/getUserById/${userId}`)
}